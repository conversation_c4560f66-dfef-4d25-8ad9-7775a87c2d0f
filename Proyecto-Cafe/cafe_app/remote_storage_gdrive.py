"""Sincronización simple de cafe.db con Google Drive usando Service Account.

Notas importantes:
- Este módulo usa la API de Drive con una Service Account.
- Requiere dos secrets: GDRIVE_SERVICE_ACCOUNT_JSON y GDRIVE_FOLDER_ID
- Sube/descarga un archivo `cafe.db` dentro de la carpeta indicada.

Limitaciones: esto no es sincronización en tiempo real; es un respaldo best‑effort.
"""
from __future__ import annotations

import io
import json
import os
from typing import Optional

from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.http import MediaIoBaseDownload, MediaIoBaseUpload

FILENAME = "cafe.db"


def _get_drive_service() -> Optional[any]:
    svc_json = os.environ.get("GDRIVE_SERVICE_ACCOUNT_JSON")
    folder_id = os.environ.get("GDRIVE_FOLDER_ID")
    if not svc_json or not folder_id:
        return None
    try:
        info = json.loads(svc_json)
        creds = service_account.Credentials.from_service_account_info(info, scopes=[
            "https://www.googleapis.com/auth/drive",
            "https://www.googleapis.com/auth/drive.file",
        ])
        return build("drive", "v3", credentials=creds)
    except Exception:
        return None


def _find_file_id(drive, name: str, folder_id: str) -> Optional[str]:
    q = f"name='{name}' and '{folder_id}' in parents and trashed=false"
    res = drive.files().list(q=q, fields="files(id,name)").execute()
    files = res.get("files", [])
    if not files:
        return None
    return files[0]["id"]


def download_db_if_missing(db_path: str = FILENAME) -> bool:
    drive = _get_drive_service()
    folder_id = os.environ.get("GDRIVE_FOLDER_ID")
    if not drive or not folder_id:
        return False
    if os.path.exists(db_path):
        return False
    file_id = _find_file_id(drive, os.path.basename(db_path), folder_id)
    if not file_id:
        return False
    request = drive.files().get_media(fileId=file_id)
    fh = io.BytesIO()
    downloader = MediaIoBaseDownload(fh, request)
    done = False
    while not done:
        status, done = downloader.next_chunk()
    with open(db_path, "wb") as f:
        f.write(fh.getbuffer())
    return True


def upload_db(db_path: str = FILENAME) -> bool:
    drive = _get_drive_service()
    folder_id = os.environ.get("GDRIVE_FOLDER_ID")
    if not drive or not folder_id:
        return False
    if not os.path.exists(db_path):
        return False
    file_id = _find_file_id(drive, os.path.basename(db_path), folder_id)
    media = MediaIoBaseUpload(open(db_path, "rb"), mimetype="application/octet-stream", chunksize=1024*1024, resumable=True)
    if file_id:
        drive.files().update(fileId=file_id, media_body=media).execute()
    else:
        metadata = {"name": os.path.basename(db_path), "parents": [folder_id]}
        drive.files().create(body=metadata, media_body=media, fields="id").execute()
    return True

