import streamlit as st
from datetime import datetime
import pandas as pd

from cafe_app.database import (
    init_db,
    upsert_cliente,
    search_clientes,
    get_cliente_by_id,
    add_cafe_venta,
    obtener_datos_financieros,
    update_cliente_by_id,
    set_cliente_activo,
    count_cxc_pendientes_cliente if False else None,
    upsert_inventario_item,
    get_inventario_items,
    set_cliente_activo as _set_cliente_activo,
    upsert_proveedor,
    add_cafe_compra,
)

# Intento de descarga desde Google Drive si no existe la BD
try:
    from cafe_app.remote_storage_gdrive import download_db_if_missing, upload_db
    download_db_if_missing("cafe.db")
except Exception:
    pass

st.set_page_config(page_title="Proyecto Café", layout="wide")
st.title("Proyecto Café")

init_db()

seccion = st.sidebar.radio("Secciones", ["Ventas", "Compras", "Clientes", "Reportes", "Inventario"], key="cafe_seccion")

if seccion == "Clientes":
    st.header("Gestión de clientes")
    with st.expander("Nuevo cliente"):
        col1, col2 = st.columns(2)
        with col1:
            n_nombres = st.text_input("Nombres")
            n_cel = st.text_input("Celular")
        with col2:
            n_apellidos = st.text_input("Apellidos")
            n_correo = st.text_input("Correo (opcional)")
        n_dir = st.text_input("Dirección (opcional)")
        cumple = st.date_input("Cumpleaños (opcional)", value=None)
        cumple_str = cumple.strftime('%Y-%m-%d') if cumple else None
        if st.button("Guardar cliente"):
            if n_nombres and n_apellidos and n_cel:
                upsert_cliente(n_nombres, n_apellidos, n_cel, n_correo or None, n_dir or None, cumple_str)
                st.success("Cliente guardado/actualizado.")
            else:
                st.error("Completa Nombre, Apellido y Celular.")

    st.subheader("Buscar clientes")
    colb1, colb2 = st.columns([3,1])
    q = colb1.text_input("Buscar por nombre/apellido/celular")
    include_inactive = colb2.checkbox("Incluir inactivos", value=False)
    if q:
        st.dataframe(search_clientes(q, include_inactive=include_inactive))

    with st.expander("Editar o inactivar cliente"):
        q_edit = st.text_input("Buscar por nombre/apellido/celular")
        if q_edit:
            df_res = search_clientes(q_edit, include_inactive=True)
            if not df_res.empty:
                etiquetas = [f"{row.nombres} {row.apellidos} – {row.celular} ({'Activo' if row.activo==1 else 'Inactivo'})" for _, row in df_res.iterrows()]
                ids = df_res['id'].tolist()
                sel_id = st.selectbox("Selecciona cliente", options=ids, format_func=lambda cid: etiquetas[ids.index(cid)])
                if sel_id:
                    cli = get_cliente_by_id(int(sel_id))
                    if cli:
                        col1, col2 = st.columns(2)
                        with col1:
                            e_nombres = st.text_input("Nombres", value=cli['nombres'])
                            e_cel = st.text_input("Celular", value=cli['celular'])
                        with col2:
                            e_apellidos = st.text_input("Apellidos", value=cli['apellidos'])
                            e_correo = st.text_input("Correo", value=cli.get('correo') or '')
                        e_dir = st.text_input("Dirección", value=cli.get('direccion') or '')
                        e_cumple = st.date_input("Cumpleaños", value=datetime.strptime(cli['cumple'], '%Y-%m-%d') if cli.get('cumple') else None)
                        if st.button("Guardar cambios"):
                            update_cliente_by_id(int(sel_id), e_nombres, e_apellidos, e_cel, e_correo or None, e_dir or None, e_cumple.strftime('%Y-%m-%d') if e_cumple else None)
                            st.success("Cambios guardados.")
                        if st.button("Inactivar cliente"):
                            _set_cliente_activo(int(sel_id), False)
                            st.success("Cliente inactivado.")

elif seccion == "Ventas":
    st.header("Registrar venta")
    tab = st.tabs(["Contado", "Crédito"])
    with tab[0]:
        # Venta contado
        col = st.columns(2)
        with col[0]:
            fecha = st.date_input("Fecha", value=datetime.today())
            item = st.text_input("Producto")
            unidades = st.number_input("Unidades", min_value=0.0, step=0.5)
            medida = st.text_input("Medida", value="lb")
            precio_total = st.number_input("Precio total", min_value=0.0, step=100.0)
            costo_unitario = st.number_input("Costo unitario", min_value=0.0, step=100.0)
        with col[1]:
            metodo_pago = st.selectbox("Método", ["Efectivo", "Nequi", "Daviplata", "Cuenta Bancaria"])
            obs = st.text_area("Observaciones", value="")
        if st.button("Guardar venta (contado)"):
            add_cafe_venta(fecha.strftime('%Y-%m-%d'), None, item, unidades, medida, precio_total, costo_unitario, obs or None, "contado", metodo_pago)
            st.success("Venta registrada.")
    with tab[1]:
        # Venta crédito
        col = st.columns(2)
        with col[0]:
            fecha = st.date_input("Fecha", value=datetime.today(), key="vcred_fecha")
            item = st.text_input("Producto", key="vcred_item")
            unidades = st.number_input("Unidades", min_value=0.0, step=0.5, key="vcred_unid")
            medida = st.text_input("Medida", value="lb", key="vcred_med")
            precio_total = st.number_input("Precio total", min_value=0.0, step=100.0, key="vcred_precio")
            costo_unitario = st.number_input("Costo unitario", min_value=0.0, step=100.0, key="vcred_costo")
        with col[1]:
            q_cli = st.text_input("Buscar cliente por nombre/celular", key="vcred_buscar")
            df_cli = search_clientes(q_cli or "", include_inactive=False)
            sel_id = None
            if not df_cli.empty:
                etiquetas = [f"{row.nombres} {row.apellidos} – {row.celular}" for _, row in df_cli.iterrows()]
                id_list = df_cli['id'].tolist()
                sel_id = st.selectbox("Selecciona cliente", options=id_list, format_func=lambda cid: etiquetas[id_list.index(cid)])
            venc = st.date_input("Vencimiento (opcional)", value=None)
            obs = st.text_area("Observaciones", value="", key="vcred_obs")
        if st.button("Guardar venta (crédito)"):
            add_cafe_venta(fecha.strftime('%Y-%m-%d'), int(sel_id) if sel_id else None, item, unidades, medida, precio_total, costo_unitario, obs or None, "crédito", None, venc.strftime('%Y-%m-%d') if venc else None)
            st.success("Venta a crédito registrada.")

elif seccion == "Compras":
    st.header("Registrar compra")
    col = st.columns(2)
    with col[0]:
        fecha = st.date_input("Fecha", value=datetime.today(), key="c_fecha")
        item = st.text_input("Item", key="c_item")
        unidades = st.number_input("Unidades", min_value=0.0, step=0.5, key="c_unid")
        medida = st.text_input("Medida", value="lb", key="c_med")
        costo_unitario = st.number_input("Costo unitario", min_value=0.0, step=100.0, key="c_costo")
    with col[1]:
        q_prov = st.text_input("Proveedor (buscar o nuevo)", key="c_prov_q")
        pid = None
        if q_prov:
            pid = upsert_proveedor(q_prov)
        obs = st.text_area("Observaciones", value="", key="c_obs")
    if st.button("Guardar compra"):
        add_cafe_compra(fecha.strftime('%Y-%m-%d'), pid, item, unidades, medida, costo_unitario, obs or None)
        st.success("Compra registrada.")

elif seccion == "Reportes":
    st.header("Reportes básicos")
    df = obtener_datos_financieros()
    st.dataframe(df)

elif seccion == "Inventario":
    st.header("Catálogo de ítems de inventario")
    with st.expander("Nuevo ítem"):
        col1, col2 = st.columns(2)
        with col1:
            ni_nombre = st.text_input("Nombre del ítem")
        with col2:
            ni_medida = st.text_input("Medida", value="lb")
        if st.button("Guardar ítem"):
            if ni_nombre:
                upsert_inventario_item(ni_nombre, ni_medida or None, True)
                st.success("Ítem guardado/actualizado")
            else:
                st.error("El nombre es obligatorio")
    st.subheader("Ítems activos")
    df_inv = get_inventario_items(include_inactive=True)
    if not df_inv.empty:
        st.dataframe(df_inv)

# Botón de respaldo manual a Drive
st.divider()
st.caption("Respaldo opcional a Google Drive")
try:
    if st.button("Sincronizar con Google Drive ahora"):
        from cafe_app.remote_storage_gdrive import upload_db
        if upload_db("cafe.db"):
            st.success("Base de datos respaldada en Google Drive.")
        else:
            st.warning("No se pudo respaldar. Revisa credenciales/valores.")
except Exception:
    pass

