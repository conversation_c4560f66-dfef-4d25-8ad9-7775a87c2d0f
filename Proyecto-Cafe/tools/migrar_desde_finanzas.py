"""Script de migración: copia datos de Café desde finanzas_familiares.db a cafe.db

Uso:
  python -m tools.migrar_desde_finanzas --origen ../finanzasfamilia/finanzas_familiares.db
"""
from __future__ import annotations

import argparse
import shutil
import sqlite3
from pathlib import Path

from cafe_app.database import init_db, DB_PATH

TABLAS = [
    "clientes",
    "proveedores",
    "cafe_ventas",
    "cafe_compras",
    "cafe_cxc",
    "inventario_items",
]


def copiar_tabla(origen: Path, destino: Path, tabla: str):
    src = sqlite3.connect(str(origen))
    dst = sqlite3.connect(str(destino))
    sc = src.cursor()
    dc = dst.cursor()
    # Leer estructura
    sc.execute(f"SELECT sql FROM sqlite_master WHERE type='table' AND name=?", (tabla,))
    row = sc.fetchone()
    if not row:
        src.close()
        dst.close()
        return
    # Asegurar tabla existe en destino (init_db ya crea todas)
    # Copiar datos
    sc.execute(f"SELECT * FROM {tabla}")
    rows = sc.fetchall()
    if not rows:
        src.close()
        dst.close()
        return
    sc.execute(f"PRAGMA table_info({tabla})")
    cols = [r[1] for r in sc.fetchall()]
    placeholders = ",".join(["?"] * len(cols))
    dc.executemany(f"INSERT INTO {tabla} ({','.join(cols)}) VALUES ({placeholders})", rows)
    dst.commit()
    src.close()
    dst.close()


def copiar_transacciones_cafe(origen: Path, destino: Path):
    # Heurística: copiar transacciones con categoria en ('Ventas','Negocio','Activos') y descripciones que contengan 'café' o 'inventario'
    src = sqlite3.connect(str(origen))
    dst = sqlite3.connect(str(destino))
    sc = src.cursor()
    dc = dst.cursor()
    try:
        sc.execute(
            """
            SELECT * FROM transacciones
            WHERE lower(descripcion) LIKE '%café%' OR lower(descripcion) LIKE '%cafe%'
               OR lower(categoria) IN ('ventas','negocio','activos')
            """
        )
        rows = sc.fetchall()
        if rows:
            sc.execute("PRAGMA table_info(transacciones)")
            cols = [r[1] for r in sc.fetchall()]
            placeholders = ",".join(["?"] * len(cols))
            dc.executemany(f"INSERT INTO transacciones ({','.join(cols)}) VALUES ({placeholders})", rows)
            dst.commit()
    finally:
        src.close()
        dst.close()


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--origen", required=True, help="Ruta a finanzas_familiares.db")
    args = parser.parse_args()
    origen = Path(args.origen).resolve()
    destino = Path(DB_PATH).resolve()

    # Asegurar DB destino
    init_db(str(destino))

    # Copiar tablas principales de Café
    for t in TABLAS:
        copiar_tabla(origen, destino, t)

    # Copiar transacciones asociadas
    copiar_transacciones_cafe(origen, destino)

    print("Migración completada hacia:", destino)


if __name__ == "__main__":
    main()

